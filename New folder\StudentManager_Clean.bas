Attribute VB_Name = "StudentManager"
' ===================================================================
' Module: StudentManager
' Description: Main module for student information management
' Author: Auto Generated
' Date: 2025-01-23
' ===================================================================

Option Explicit

' Configuration constants
Public Const MAIN_SHEET_NAME As String = "DanhSachHocVien"
Public Const HISTORY_SHEET_NAME As String = "LichSuThayDoi"
Public Const BACKUP_FOLDER As String = "Backup"

' Data column structure
Public Enum StudentColumns
    STT = 1
    HoTen = 2
    NgaySinhNam = 3
    NgaySinhNu = 4
    ChucVu = 5
    DonVi = 6
    LopHoc = 7
End Enum

' Student data structure
Public Type StudentInfo
    STT As Long
    HoTen As String
    NgaySinhNam As String
    NgaySinhNu As String
    ChucVu As String
    DonVi As String
    LopHoc As String
End Type

' ===================================================================
' Function: InitializeWorkbook
' Description: Initialize workbook with required sheets
' ===================================================================
Sub InitializeWorkbook()
    On Error GoTo ErrorHandler
    
    DebugLogSafe "InitializeWorkbook: Start initializing workbook"
    
    ' Create main sheet if not exists
    If Not SheetExists(MAIN_SHEET_NAME) Then
        CreateMainSheet
        DebugLogSafe "InitializeWorkbook: Created main sheet"
    End If
    
    ' Create history sheet if not exists
    If Not SheetExists(HISTORY_SHEET_NAME) Then
        CreateHistorySheet
        DebugLogSafe "InitializeWorkbook: Created history sheet"
    End If
    
    ' Setup events
    SetupWorksheetEvents
    
    DebugLogSafe "InitializeWorkbook: Initialization completed"
    MsgBox "Student management system initialized successfully!", vbInformation, "Notification"
    
    Exit Sub

ErrorHandler:
    DebugLogSafe "InitializeWorkbook: Error - " & Err.Description
    MsgBox "Initialization error: " & Err.Description, vbCritical, "Error"
End Sub

' ===================================================================
' Function: CreateMainSheet
' Description: Create main sheet with data structure
' ===================================================================
Sub CreateMainSheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets.Add
    ws.Name = MAIN_SHEET_NAME
    
    ' Setup column headers
    With ws
        .Cells(1, StudentColumns.STT).Value = "STT"
        .Cells(1, StudentColumns.HoTen).Value = "Ho va ten"
        .Cells(1, StudentColumns.NgaySinhNam).Value = "Ngay sinh (Nam)"
        .Cells(1, StudentColumns.NgaySinhNu).Value = "Ngay sinh (Nu)"
        .Cells(1, StudentColumns.ChucVu).Value = "Chuc vu"
        .Cells(1, StudentColumns.DonVi).Value = "Don vi"
        .Cells(1, StudentColumns.LopHoc).Value = "Lop hoc"
        
        ' Format headers
        With .Range(.Cells(1, 1), .Cells(1, 7))
            .Font.Bold = True
            .Interior.Color = RGB(200, 200, 200)
            .HorizontalAlignment = xlCenter
            .Borders.LineStyle = xlContinuous
        End With
        
        ' Set column widths
        .Columns(StudentColumns.STT).ColumnWidth = 5
        .Columns(StudentColumns.HoTen).ColumnWidth = 25
        .Columns(StudentColumns.NgaySinhNam).ColumnWidth = 15
        .Columns(StudentColumns.NgaySinhNu).ColumnWidth = 15
        .Columns(StudentColumns.ChucVu).ColumnWidth = 15
        .Columns(StudentColumns.DonVi).ColumnWidth = 20
        .Columns(StudentColumns.LopHoc).ColumnWidth = 10
        
        ' Freeze header row
        .Rows(2).Select
        ActiveWindow.FreezePanes = True
    End With
    
    DebugLogSafe "CreateMainSheet: Main sheet created successfully"
    Exit Sub

ErrorHandler:
    DebugLogSafe "CreateMainSheet: Error - " & Err.Description
End Sub

' ===================================================================
' Function: CreateHistorySheet
' Description: Create history sheet for change tracking
' ===================================================================
Sub CreateHistorySheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets.Add
    ws.Name = HISTORY_SHEET_NAME
    
    ' Setup history column headers
    With ws
        .Cells(1, 1).Value = "Thoi gian"
        .Cells(1, 2).Value = "Nguoi thuc hien"
        .Cells(1, 3).Value = "Hanh dong"
        .Cells(1, 4).Value = "Vi tri (Cell)"
        .Cells(1, 5).Value = "Gia tri cu"
        .Cells(1, 6).Value = "Gia tri moi"
        .Cells(1, 7).Value = "Ghi chu"
        
        ' Format headers
        With .Range(.Cells(1, 1), .Cells(1, 7))
            .Font.Bold = True
            .Interior.Color = RGB(255, 255, 200)
            .HorizontalAlignment = xlCenter
            .Borders.LineStyle = xlContinuous
        End With
        
        ' Set column widths
        .Columns(1).ColumnWidth = 20  ' Time
        .Columns(2).ColumnWidth = 15  ' User
        .Columns(3).ColumnWidth = 15  ' Action
        .Columns(4).ColumnWidth = 10  ' Position
        .Columns(5).ColumnWidth = 20  ' Old value
        .Columns(6).ColumnWidth = 20  ' New value
        .Columns(7).ColumnWidth = 30  ' Notes
        
        ' Freeze header row
        .Rows(2).Select
        ActiveWindow.FreezePanes = True
    End With
    
    DebugLogSafe "CreateHistorySheet: History sheet created successfully"
    Exit Sub

ErrorHandler:
    DebugLogSafe "CreateHistorySheet: Error - " & Err.Description
End Sub

' ===================================================================
' Function: SheetExists
' Description: Check if sheet exists
' Parameter: sheetName - Name of sheet to check
' Return: True if sheet exists, False if not
' ===================================================================
Function SheetExists(ByVal sheetName As String) As Boolean
    On Error Resume Next
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(sheetName)
    
    SheetExists = Not ws Is Nothing
    
    On Error GoTo 0
End Function

' ===================================================================
' Function: SetupWorksheetEvents
' Description: Setup worksheet events
' ===================================================================
Sub SetupWorksheetEvents()
    On Error GoTo ErrorHandler
    
    ' Enable worksheet events
    Application.EnableEvents = True
    
    DebugLogSafe "SetupWorksheetEvents: Worksheet events setup completed"
    Exit Sub

ErrorHandler:
    DebugLogSafe "SetupWorksheetEvents: Error - " & Err.Description
End Sub

' ===================================================================
' Function: AddSampleData
' Description: Add sample data for testing
' ===================================================================
Sub AddSampleData()
    On Error GoTo ErrorHandler
    
    DebugLogSafe "AddSampleData: Start adding sample data"
    
    If Not SheetExists(MAIN_SHEET_NAME) Then
        MsgBox "Please initialize the system first!", vbExclamation, "Notification"
        Exit Sub
    End If
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(MAIN_SHEET_NAME)
    
    ' Add sample data
    With ws
        .Cells(2, StudentColumns.STT).Value = 1
        .Cells(2, StudentColumns.HoTen).Value = "Nguyen Van An"
        .Cells(2, StudentColumns.NgaySinhNam).Value = "01/01/1990"
        .Cells(2, StudentColumns.NgaySinhNu).Value = ""
        .Cells(2, StudentColumns.ChucVu).Value = "Nhan vien"
        .Cells(2, StudentColumns.DonVi).Value = "Phong IT"
        .Cells(2, StudentColumns.LopHoc).Value = "A1"
        
        .Cells(3, StudentColumns.STT).Value = 2
        .Cells(3, StudentColumns.HoTen).Value = "Tran Thi Binh"
        .Cells(3, StudentColumns.NgaySinhNam).Value = ""
        .Cells(3, StudentColumns.NgaySinhNu).Value = "15/05/1992"
        .Cells(3, StudentColumns.ChucVu).Value = "Truong phong"
        .Cells(3, StudentColumns.DonVi).Value = "Phong Ke toan"
        .Cells(3, StudentColumns.LopHoc).Value = "A2"
        
        .Cells(4, StudentColumns.STT).Value = 3
        .Cells(4, StudentColumns.HoTen).Value = "Le Minh Cuong"
        .Cells(4, StudentColumns.NgaySinhNam).Value = "20/12/1988"
        .Cells(4, StudentColumns.NgaySinhNu).Value = ""
        .Cells(4, StudentColumns.ChucVu).Value = "Giam doc"
        .Cells(4, StudentColumns.DonVi).Value = "Ban Giam doc"
        .Cells(4, StudentColumns.LopHoc).Value = "A1"
    End With
    
    DebugLogSafe "AddSampleData: Sample data added successfully"
    MsgBox "Sample data added successfully!", vbInformation, "Notification"
    
    Exit Sub

ErrorHandler:
    DebugLogSafe "AddSampleData: Error - " & Err.Description
    MsgBox "Error adding sample data: " & Err.Description, vbCritical, "Error"
End Sub

' ===================================================================
' Function: TestStudentManager
' Description: Test student manager module
' ===================================================================
Sub TestStudentManager()
    DebugLogSafe "=== START TEST STUDENT MANAGER ==="
    
    ' Test initialization
    InitializeWorkbook
    
    ' Test adding sample data
    AddSampleData
    
    DebugLogSafe "=== END TEST STUDENT MANAGER ==="
End Sub
