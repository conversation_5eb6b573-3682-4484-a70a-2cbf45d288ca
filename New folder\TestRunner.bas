Attribute VB_Name = "TestRunner"
' ===================================================================
' Module: TestRunner
' Mô tả: Module chạy test tổng hợp
' Tác giả: Auto Generated
' Ngày tạo: 2025-01-23
' ===================================================================

Option Explicit

' ===================================================================
' Hàm: RunAllTests
' Mô tả: Chạy tất cả các test
' ===================================================================
Sub RunAllTests()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "BAT DAU CHAY TAT CA CAC TEST"
    Debug.Print "========================================="
    
    ' Test 1: Vietnamese Support
    Debug.Print ""
    Debug.Print "--- TEST 1: VIETNAMESE SUPPORT ---"
    TestVietNameseSupport
    
    ' Test 2: Student Manager
    Debug.Print ""
    Debug.Print "--- TEST 2: STUDENT MANAGER ---"
    TestStudentManager
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH TAT CA CAC TEST"
    Debug.Print "========================================="
    
    MsgBox "Tat ca cac test da hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG QUA TRINH TEST: " & Err.Description
    MsgBox "Loi trong qua trinh test: " & Err.Description, vbCritical, "Test Error"
End Sub

' ===================================================================
' Hàm: QuickTest
' Mô tả: Test nhanh các chức năng cơ bản
' ===================================================================
Sub QuickTest()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "BAT DAU QUICK TEST"
    Debug.Print "========================================="
    
    ' Test ký tự tiếng Việt cơ bản
    Debug.Print "Test ky tu tieng Viet:"
    Debug.Print "- Ky tu 'a' (thuong): " & isVNChar("ă")
    Debug.Print "- Ky tu 'a' (binh thuong): " & isVNChar("a")
    Debug.Print "- Ky tu 'd' (bar): " & isVNChar("đ")
    
    ' Test chuyển đổi chuỗi đơn giản
    Debug.Print ""
    Debug.Print "Test chuyen doi chuoi:"
    Dim testStr As String
    testStr = "Hello World"
    Debug.Print "- Chuoi ASCII: " & testStr & " -> " & convertVNtoVBA(testStr)
    
    ' Test kiểm tra sheet
    Debug.Print ""
    Debug.Print "Test kiem tra sheet:"
    Debug.Print "- Sheet 'DanhSachHocVien' ton tai: " & SheetExists("DanhSachHocVien")
    Debug.Print "- Sheet 'LichSuThayDoi' ton tai: " & SheetExists("LichSuThayDoi")
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH QUICK TEST"
    Debug.Print "========================================="
    
    MsgBox "Quick test hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Quick Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG QUICK TEST: " & Err.Description
    MsgBox "Loi trong quick test: " & Err.Description, vbCritical, "Quick Test Error"
End Sub

' ===================================================================
' Hàm: TestUnicodeChars
' Mô tả: Test chi tiết các ký tự Unicode tiếng Việt
' ===================================================================
Sub TestUnicodeChars()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "BAT DAU TEST UNICODE CHARACTERS"
    Debug.Print "========================================="
    
    ' Danh sách ký tự test
    Dim testChars As Variant
    testChars = Array("a", "ă", "â", "e", "ê", "i", "o", "ô", "ơ", "u", "ư", "y", "đ", _
                     "A", "Ă", "Â", "E", "Ê", "I", "O", "Ô", "Ơ", "U", "Ư", "Y", "Đ")
    
    Dim i As Long
    For i = 0 To UBound(testChars)
        Dim char As String
        char = CStr(testChars(i))
        Dim charCode As Long
        charCode = AscW(char)
        Dim isVN As Boolean
        isVN = isVNChar(char)
        
        Debug.Print "Char: " & ConvertToSafeASCII(char) & " | Code: " & charCode & " | IsVN: " & isVN
    Next i
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH TEST UNICODE CHARACTERS"
    Debug.Print "========================================="
    
    MsgBox "Unicode test hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Unicode Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG UNICODE TEST: " & Err.Description
    MsgBox "Loi trong unicode test: " & Err.Description, vbCritical, "Unicode Test Error"
End Sub

' ===================================================================
' Hàm: ClearDebugWindow
' Mô tả: Xóa nội dung Immediate Window (chỉ có thể làm thủ công)
' ===================================================================
Sub ClearDebugWindow()
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "DE XOA IMMEDIATE WINDOW:"
    Debug.Print "1. Nhan Ctrl+G de mo Immediate Window"
    Debug.Print "2. Chon tat ca noi dung (Ctrl+A)"
    Debug.Print "3. Nhan Delete"
    Debug.Print "========================================="
    Debug.Print ""
End Sub
