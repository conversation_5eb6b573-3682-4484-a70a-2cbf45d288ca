Attribute VB_Name = "TestRunner"
' ===================================================================
' Module: TestRunner
' Mô tả: Module chạy test tổng hợp
' Tác giả: Auto Generated
' Ngày tạo: 2025-01-23
' ===================================================================

Option Explicit

' ===================================================================
' Hàm: RunAllTests
' Mô tả: Chạy tất cả các test
' ===================================================================
Sub RunAllTests()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "BAT DAU CHAY TAT CA CAC TEST"
    Debug.Print "========================================="
    
    ' Test 1: Vietnamese Support
    Debug.Print ""
    Debug.Print "--- TEST 1: VIETNAMESE SUPPORT ---"
    TestVietNameseSupport
    
    ' Test 2: Student Manager
    Debug.Print ""
    Debug.Print "--- TEST 2: STUDENT MANAGER ---"
    TestStudentManager
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH TAT CA CAC TEST"
    Debug.Print "========================================="
    
    MsgBox "Tat ca cac test da hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG QUA TRINH TEST: " & Err.Description
    MsgBox "Loi trong qua trinh test: " & Err.Description, vbCritical, "Test Error"
End Sub

' ===================================================================
' Hàm: QuickTest
' Mô tả: Test nhanh các chức năng cơ bản
' ===================================================================
Sub QuickTest()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "BAT DAU QUICK TEST"
    Debug.Print "========================================="
    
    ' Test ký tự tiếng Việt cơ bản (sử dụng mã Unicode)
    Debug.Print "Test ky tu tieng Viet:"
    Debug.Print "- Ky tu 'a' (thuong - U+0103): " & isVNChar(ChrW(259))  ' ă
    Debug.Print "- Ky tu 'a' (binh thuong): " & isVNChar("a")
    Debug.Print "- Ky tu 'd' (bar - U+0111): " & isVNChar(ChrW(273))     ' đ
    
    ' Test chuyển đổi chuỗi đơn giản
    Debug.Print ""
    Debug.Print "Test chuyen doi chuoi:"
    Dim testStr As String
    testStr = "Hello World"
    Debug.Print "- Chuoi ASCII: " & testStr & " -> " & convertVNtoVBA(testStr)
    
    ' Test kiểm tra sheet
    Debug.Print ""
    Debug.Print "Test kiem tra sheet:"
    Debug.Print "- Sheet 'DanhSachHocVien' ton tai: " & SheetExists("DanhSachHocVien")
    Debug.Print "- Sheet 'LichSuThayDoi' ton tai: " & SheetExists("LichSuThayDoi")
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH QUICK TEST"
    Debug.Print "========================================="
    
    MsgBox "Quick test hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Quick Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG QUICK TEST: " & Err.Description
    MsgBox "Loi trong quick test: " & Err.Description, vbCritical, "Quick Test Error"
End Sub

' ===================================================================
' Hàm: TestUnicodeChars
' Mô tả: Test chi tiết các ký tự Unicode tiếng Việt
' ===================================================================
Sub TestUnicodeChars()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "BAT DAU TEST UNICODE CHARACTERS"
    Debug.Print "========================================="
    
    ' Danh sách ký tự test (sử dụng mã Unicode)
    Dim testChars As Variant
    Dim testNames As Variant

    ' Mảng ký tự Unicode
    testChars = Array("a", ChrW(259), ChrW(226), "e", ChrW(234), "i", "o", ChrW(244), ChrW(417), "u", ChrW(432), "y", ChrW(273), _
                     "A", ChrW(258), ChrW(194), "E", ChrW(202), "I", "O", ChrW(212), ChrW(416), "U", ChrW(431), "Y", ChrW(272))

    ' Mảng tên tương ứng
    testNames = Array("a", "ă(259)", "â(226)", "e", "ê(234)", "i", "o", "ô(244)", "ơ(417)", "u", "ư(432)", "y", "đ(273)", _
                     "A", "Ă(258)", "Â(194)", "E", "Ê(202)", "I", "O", "Ô(212)", "Ơ(416)", "U", "Ư(431)", "Y", "Đ(272)")
    
    Dim i As Long
    For i = 0 To UBound(testChars)
        Dim char As String
        char = CStr(testChars(i))
        Dim charName As String
        charName = CStr(testNames(i))
        Dim charCode As Long
        charCode = AscW(char)
        Dim isVN As Boolean
        isVN = isVNChar(char)

        Debug.Print "Char: " & charName & " | Code: " & charCode & " | IsVN: " & isVN
    Next i
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH TEST UNICODE CHARACTERS"
    Debug.Print "========================================="
    
    MsgBox "Unicode test hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Unicode Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG UNICODE TEST: " & Err.Description
    MsgBox "Loi trong unicode test: " & Err.Description, vbCritical, "Unicode Test Error"
End Sub

' ===================================================================
' Hàm: ClearDebugWindow
' Mô tả: Xóa nội dung Immediate Window (chỉ có thể làm thủ công)
' ===================================================================
Sub ClearDebugWindow()
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "DE XOA IMMEDIATE WINDOW:"
    Debug.Print "1. Nhan Ctrl+G de mo Immediate Window"
    Debug.Print "2. Chon tat ca noi dung (Ctrl+A)"
    Debug.Print "3. Nhan Delete"
    Debug.Print "========================================="
    Debug.Print ""
End Sub

' ===================================================================
' Hàm: TestBasicUnicode
' Mô tả: Test cơ bản các ký tự Unicode tiếng Việt
' ===================================================================
Sub TestBasicUnicode()
    On Error GoTo ErrorHandler

    Debug.Print "========================================="
    Debug.Print "BAT DAU TEST BASIC UNICODE"
    Debug.Print "========================================="

    ' Test các ký tự cơ bản
    Debug.Print "Test cac ky tu co ban:"
    Debug.Print "- a (97): " & isVNChar("a") & " | Expected: False"
    Debug.Print "- ă (259): " & isVNChar(ChrW(259)) & " | Expected: True"
    Debug.Print "- â (226): " & isVNChar(ChrW(226)) & " | Expected: True"
    Debug.Print "- đ (273): " & isVNChar(ChrW(273)) & " | Expected: True"
    Debug.Print "- ê (234): " & isVNChar(ChrW(234)) & " | Expected: True"
    Debug.Print "- ô (244): " & isVNChar(ChrW(244)) & " | Expected: True"
    Debug.Print "- ơ (417): " & isVNChar(ChrW(417)) & " | Expected: True"
    Debug.Print "- ư (432): " & isVNChar(ChrW(432)) & " | Expected: True"

    ' Test chữ hoa
    Debug.Print ""
    Debug.Print "Test chu hoa:"
    Debug.Print "- A (65): " & isVNChar("A") & " | Expected: False"
    Debug.Print "- Ă (258): " & isVNChar(ChrW(258)) & " | Expected: True"
    Debug.Print "- Â (194): " & isVNChar(ChrW(194)) & " | Expected: True"
    Debug.Print "- Đ (272): " & isVNChar(ChrW(272)) & " | Expected: True"

    ' Test tạo chuỗi tiếng Việt
    Debug.Print ""
    Debug.Print "Test tao chuoi tieng Viet:"
    Dim vnString As String
    vnString = "Vi" & ChrW(7879) & "t Nam"  ' Việt Nam
    Debug.Print "Chuoi: " & ConvertToSafeASCII(vnString)
    Debug.Print "Convert VBA: " & convertVNtoVBA(vnString)

    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "HOAN THANH TEST BASIC UNICODE"
    Debug.Print "========================================="

    MsgBox "Basic Unicode test hoan thanh! Kiem tra Immediate Window de xem ket qua.", vbInformation, "Basic Unicode Test Complete"

    Exit Sub

ErrorHandler:
    Debug.Print "LOI TRONG BASIC UNICODE TEST: " & Err.Description
    MsgBox "Loi trong basic unicode test: " & Err.Description, vbCritical, "Basic Unicode Test Error"
End Sub
