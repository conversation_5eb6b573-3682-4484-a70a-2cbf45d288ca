Attribute VB_Name = "StudentManager"
' ===================================================================
' Module: StudentManager
' Mô tả: Module chính quản lý thông tin học viên
' Tác giả: Auto Generated
' Ngày tạo: 2025-01-23
' ===================================================================

Option Explicit

' Hằng số cấu hình
Public Const MAIN_SHEET_NAME As String = "DanhSachHocVien"
Public Const HISTORY_SHEET_NAME As String = "LichSuThayDoi"
Public Const BACKUP_FOLDER As String = "Backup"

' Cấu trúc cột dữ liệu
Public Enum StudentColumns
    STT = 1
    HoTen = 2
    NgaySinhNam = 3
    NgaySinhNu = 4
    ChucVu = 5
    DonVi = 6
    LopHoc = 7
End Enum

' Cấu trúc dữ liệu học viên
Public Type StudentInfo
    STT As Long
    HoTen As String
    NgaySinhNam As String
    NgaySinhNu As String
    ChucVu As String
    DonVi As String
    LopHoc As String
End Type

' ===================================================================
' Hàm: InitializeWorkbook
' Mô tả: Khởi tạo workbook với các sheet cần thiết
' ===================================================================
Sub InitializeWorkbook()
    On Error GoTo ErrorHandler

    DebugLogSafe "InitializeWorkbook: Bat dau khoi tao workbook"

    ' Tạo sheet chính nếu chưa có
    If Not SheetExists(MAIN_SHEET_NAME) Then
        CreateMainSheet
        DebugLogSafe "InitializeWorkbook: Da tao sheet chinh"
    End If

    ' Tạo sheet lịch sử nếu chưa có
    If Not SheetExists(HISTORY_SHEET_NAME) Then
        CreateHistorySheet
        DebugLogSafe "InitializeWorkbook: Da tao sheet lich su"
    End If

    ' Thiết lập sự kiện
    SetupWorksheetEvents

    DebugLogSafe "InitializeWorkbook: Hoan thanh khoi tao"
    MsgBox "Khoi tao he thong quan ly hoc vien thanh cong!", vbInformation, "Thong bao"

    Exit Sub

ErrorHandler:
    DebugLogSafe "InitializeWorkbook: Loi - " & Err.Description
    MsgBox "Loi khoi tao: " & Err.Description, vbCritical, "Loi"
End Sub

' ===================================================================
' Hàm: CreateMainSheet
' Mô tả: Tạo sheet chính với cấu trúc dữ liệu
' ===================================================================
Sub CreateMainSheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets.Add
    ws.Name = MAIN_SHEET_NAME
    
    ' Thiết lập tiêu đề cột
    With ws
        .Cells(1, StudentColumns.STT).Value = "STT"
        .Cells(1, StudentColumns.HoTen).Value = "Họ và tên"
        .Cells(1, StudentColumns.NgaySinhNam).Value = "Ngày sinh (Nam)"
        .Cells(1, StudentColumns.NgaySinhNu).Value = "Ngày sinh (Nữ)"
        .Cells(1, StudentColumns.ChucVu).Value = "Chức vụ"
        .Cells(1, StudentColumns.DonVi).Value = "Đơn vị"
        .Cells(1, StudentColumns.LopHoc).Value = "Lớp học"
        
        ' Định dạng tiêu đề
        With .Range(.Cells(1, 1), .Cells(1, 7))
            .Font.Bold = True
            .Interior.Color = RGB(200, 200, 200)
            .HorizontalAlignment = xlCenter
            .Borders.LineStyle = xlContinuous
        End With
        
        ' Thiết lập độ rộng cột
        .Columns(StudentColumns.STT).ColumnWidth = 5
        .Columns(StudentColumns.HoTen).ColumnWidth = 25
        .Columns(StudentColumns.NgaySinhNam).ColumnWidth = 15
        .Columns(StudentColumns.NgaySinhNu).ColumnWidth = 15
        .Columns(StudentColumns.ChucVu).ColumnWidth = 15
        .Columns(StudentColumns.DonVi).ColumnWidth = 20
        .Columns(StudentColumns.LopHoc).ColumnWidth = 10
        
        ' Đóng băng hàng tiêu đề
        .Rows(2).Select
        ActiveWindow.FreezePanes = True
    End With
    
    DebugLogSafe "CreateMainSheet: Da tao sheet chinh thanh cong"
    Exit Sub

ErrorHandler:
    DebugLogSafe "CreateMainSheet: Loi - " & Err.Description
End Sub

' ===================================================================
' Hàm: CreateHistorySheet
' Mô tả: Tạo sheet lịch sử thay đổi
' ===================================================================
Sub CreateHistorySheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets.Add
    ws.Name = HISTORY_SHEET_NAME
    
    ' Thiết lập tiêu đề cột lịch sử
    With ws
        .Cells(1, 1).Value = "Thời gian"
        .Cells(1, 2).Value = "Người thực hiện"
        .Cells(1, 3).Value = "Hành động"
        .Cells(1, 4).Value = "Vị trí (Cell)"
        .Cells(1, 5).Value = "Giá trị cũ"
        .Cells(1, 6).Value = "Giá trị mới"
        .Cells(1, 7).Value = "Ghi chú"
        
        ' Định dạng tiêu đề
        With .Range(.Cells(1, 1), .Cells(1, 7))
            .Font.Bold = True
            .Interior.Color = RGB(255, 255, 200)
            .HorizontalAlignment = xlCenter
            .Borders.LineStyle = xlContinuous
        End With
        
        ' Thiết lập độ rộng cột
        .Columns(1).ColumnWidth = 20  ' Thời gian
        .Columns(2).ColumnWidth = 15  ' Người thực hiện
        .Columns(3).ColumnWidth = 15  ' Hành động
        .Columns(4).ColumnWidth = 10  ' Vị trí
        .Columns(5).ColumnWidth = 20  ' Giá trị cũ
        .Columns(6).ColumnWidth = 20  ' Giá trị mới
        .Columns(7).ColumnWidth = 30  ' Ghi chú
        
        ' Đóng băng hàng tiêu đề
        .Rows(2).Select
        ActiveWindow.FreezePanes = True
    End With
    
    DebugLogSafe "CreateHistorySheet: Da tao sheet lich su thanh cong"
    Exit Sub

ErrorHandler:
    DebugLogSafe "CreateHistorySheet: Loi - " & Err.Description
End Sub

' ===================================================================
' Hàm: SheetExists
' Mô tả: Kiểm tra sheet có tồn tại không
' Tham số: sheetName - Tên sheet cần kiểm tra
' Trả về: True nếu sheet tồn tại, False nếu không
' ===================================================================
Function SheetExists(ByVal sheetName As String) As Boolean
    On Error Resume Next
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(sheetName)
    
    SheetExists = Not ws Is Nothing
    
    On Error GoTo 0
End Function

' ===================================================================
' Hàm: SetupWorksheetEvents
' Mô tả: Thiết lập các sự kiện cho worksheet
' ===================================================================
Sub SetupWorksheetEvents()
    On Error GoTo ErrorHandler
    
    ' Kích hoạt sự kiện worksheet
    Application.EnableEvents = True
    
    DebugLogSafe "SetupWorksheetEvents: Da thiet lap su kien worksheet"
    Exit Sub

ErrorHandler:
    DebugLogSafe "SetupWorksheetEvents: Loi - " & Err.Description
End Sub

' ===================================================================
' Hàm: AddSampleData
' Mô tả: Thêm dữ liệu mẫu để test
' ===================================================================
Sub AddSampleData()
    On Error GoTo ErrorHandler

    DebugLogSafe "AddSampleData: Bat dau them du lieu mau"

    If Not SheetExists(MAIN_SHEET_NAME) Then
        MsgBox "Vui long khoi tao he thong truoc!", vbExclamation, "Thong bao"
        Exit Sub
    End If
    
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(MAIN_SHEET_NAME)
    
    ' Thêm dữ liệu mẫu
    With ws
        .Cells(2, StudentColumns.STT).Value = 1
        .Cells(2, StudentColumns.HoTen).Value = "Nguyễn Văn An"
        .Cells(2, StudentColumns.NgaySinhNam).Value = "01/01/1990"
        .Cells(2, StudentColumns.NgaySinhNu).Value = ""
        .Cells(2, StudentColumns.ChucVu).Value = "Nhân viên"
        .Cells(2, StudentColumns.DonVi).Value = "Phòng IT"
        .Cells(2, StudentColumns.LopHoc).Value = "A1"
        
        .Cells(3, StudentColumns.STT).Value = 2
        .Cells(3, StudentColumns.HoTen).Value = "Trần Thị Bình"
        .Cells(3, StudentColumns.NgaySinhNam).Value = ""
        .Cells(3, StudentColumns.NgaySinhNu).Value = "15/05/1992"
        .Cells(3, StudentColumns.ChucVu).Value = "Trưởng phòng"
        .Cells(3, StudentColumns.DonVi).Value = "Phòng Kế toán"
        .Cells(3, StudentColumns.LopHoc).Value = "A2"
        
        .Cells(4, StudentColumns.STT).Value = 3
        .Cells(4, StudentColumns.HoTen).Value = "Lê Minh Cường"
        .Cells(4, StudentColumns.NgaySinhNam).Value = "20/12/1988"
        .Cells(4, StudentColumns.NgaySinhNu).Value = ""
        .Cells(4, StudentColumns.ChucVu).Value = "Giám đốc"
        .Cells(4, StudentColumns.DonVi).Value = "Ban Giám đốc"
        .Cells(4, StudentColumns.LopHoc).Value = "A1"
    End With
    
    DebugLogSafe "AddSampleData: Da them du lieu mau thanh cong"
    MsgBox "Da them du lieu mau thanh cong!", vbInformation, "Thong bao"

    Exit Sub

ErrorHandler:
    DebugLogSafe "AddSampleData: Loi - " & Err.Description
    MsgBox "Loi them du lieu mau: " & Err.Description, vbCritical, "Loi"
End Sub

' ===================================================================
' Hàm: TestStudentManager
' Mô tả: Hàm test module quản lý học viên
' ===================================================================
Sub TestStudentManager()
    DebugLogSafe "=== BAT DAU TEST STUDENT MANAGER ==="

    ' Test khởi tạo
    InitializeWorkbook

    ' Test thêm dữ liệu mẫu
    AddSampleData

    DebugLogSafe "=== KET THUC TEST STUDENT MANAGER ==="
End Sub
