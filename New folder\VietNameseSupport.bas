Attribute VB_Name = "VietNameseSupport"
' ===================================================================
' Module: VietNameseSupport
' Mô tả: Hỗ trợ xử lý tiếng Việt trong VBA
' Tác giả: Auto Generated
' Ngày tạo: 2025-01-23
' ===================================================================

Option Explicit

' Biến toàn cục để debug
Public Const DEBUG_MODE As Boolean = True
Public Const LOG_FILE_PATH As String = "C:\temp\excel_addon_debug.log"

' ===================================================================
' Hàm: convertVNtoVBA
' Mô tả: Chuyển đổi chuỗi tiếng Việt sang định dạng VBA
' Tham số: str - Chuỗi cần chuyển đổi
' Trả về: Chuỗi đã được chuyển đổi
' ===================================================================
Function convertVNtoVBA(ByVal str As String) As String
    On Error GoTo ErrorHandler
    
    DebugLog "convertVNtoVBA: Bắt đầu chuyển đổi chuỗi: " & str
    
    Dim isVN1 As Boolean, isVN2 As Boolean, isVar As Boolean
    Dim indexChar As Long, indexStart As Long
    Dim s1 As String
    
    If str = vbNullString Then
        convertVNtoVBA = """"""
        DebugLog "convertVNtoVBA: Chuỗi rỗng, trả về chuỗi trống"
        Exit Function
    End If
        
    isVar = False
       
    str = Replace(str, "//", Chr(13)) & " "
    
    If isVNChar(Left(str, 1)) = False Then convertVNtoVBA = """"
    indexStart = 1
    If Left(str, 1) = "<" Then
        isVar = True
        indexStart = 2
    End If
         
    For indexChar = indexStart To Len(str) - 1
        s1 = Mid(str, indexChar, 1)
           
        If InStr("<>", s1) > 0 Then
            Select Case s1
            Case "<"
                If Not isVNChar(Mid(str, indexChar - 1, 1)) Then
                    convertVNtoVBA = convertVNtoVBA & """ & " & ""
                Else
                    convertVNtoVBA = convertVNtoVBA & " & "
                End If
                        
                isVar = True
                
            Case ">"
                If Not isVNChar(Mid(str, indexChar + 1, 1)) Then
                    convertVNtoVBA = convertVNtoVBA & " & """
                Else
                    convertVNtoVBA = convertVNtoVBA & " & "
                End If
                
                isVar = False
            End Select
        Else
                  
            Select Case isVar
            Case False
                isVN1 = isVNChar(Mid(str, indexChar, 1))
                isVN2 = isVNChar(Mid(str, indexChar + 1, 1))

                If isVN1 = True And isVN2 = True Then
                    convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") & "
                ElseIf isVN1 = True And isVN2 = False Then
                    If Mid(str, indexChar + 1, 1) <> "<" Then
                        convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") & """
                    Else
                        convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") "
                    End If
                ElseIf isVN1 = False And isVN2 = True Then
                    convertVNtoVBA = convertVNtoVBA & s1 & """ & "
                Else
                    convertVNtoVBA = convertVNtoVBA & s1
                End If
                
            Case True
            
                convertVNtoVBA = convertVNtoVBA & s1
            
            End Select
        End If
    Next indexChar

    If Right(convertVNtoVBA, 4) = " & """ Then
        convertVNtoVBA = Left(convertVNtoVBA, Len(convertVNtoVBA) - 4)
    Else
        convertVNtoVBA = convertVNtoVBA & """"
    End If
    
    DebugLog "convertVNtoVBA: Kết quả chuyển đổi: " & convertVNtoVBA
    Exit Function

ErrorHandler:
    DebugLog "convertVNtoVBA: Lỗi - " & Err.Description
    convertVNtoVBA = str ' Trả về chuỗi gốc nếu có lỗi
End Function

' ===================================================================
' Hàm: isVNChar
' Mô tả: Kiểm tra ký tự có phải là ký tự tiếng Việt không
' Tham số: str - Ký tự cần kiểm tra
' Trả về: True nếu là ký tự tiếng Việt, False nếu không
' ===================================================================
Function isVNChar(ByVal str As String) As Boolean
    On Error GoTo ErrorHandler
    
    Dim strViet As String
    Dim s As String

    strViet = ChrW(7855) & ChrW(7857) & ChrW(7859) & ChrW(7861) & ChrW(7863) & ChrW(7845) & ChrW(7847) & _
                    ChrW(7849) & ChrW(7851) & ChrW(7853) & ChrW(7871) & ChrW(7873) & ChrW(7875) & ChrW(7877) & _
                    ChrW(7879) & ChrW(7889) & ChrW(7891) & ChrW(7893) & ChrW(7895) & ChrW(7897) & ChrW(7899) & _
                    ChrW(7901) & ChrW(7903) & ChrW(7905) & ChrW(7907) & ChrW(7913) & ChrW(7915) & ChrW(7917) & _
                    ChrW(7919) & ChrW(7921) & ChrW(225) & ChrW(224) & ChrW(7843) & ChrW(227) & ChrW(7841) & _
                    ChrW(259) & ChrW(226) & ChrW(273) & ChrW(233) & ChrW(232) & ChrW(7867) & ChrW(7869) & _
                    ChrW(7865) & ChrW(234) & ChrW(237) & ChrW(236) & ChrW(7881) & ChrW(297) & ChrW(7883) & _
                    ChrW(243) & ChrW(242) & ChrW(7887) & ChrW(245) & ChrW(7885) & ChrW(244) & ChrW(417) & _
                    ChrW(250) & ChrW(249) & ChrW(7911) & ChrW(361) & ChrW(7909) & ChrW(432) & ChrW(253) & _
                    ChrW(7923) & ChrW(7927) & ChrW(7929) & ChrW(7925) & ChrW(7854) & ChrW(7856) & ChrW(7858) & _
                    ChrW(7860) & ChrW(7862) & ChrW(7844) & ChrW(7846) & ChrW(7848) & ChrW(7850) & ChrW(7852) & _
                    ChrW(7870) & ChrW(7872) & ChrW(7874) & ChrW(7876) & ChrW(7878) & ChrW(7888) & ChrW(7890) & _
                    ChrW(7892) & ChrW(7894) & ChrW(7896) & ChrW(7898) & ChrW(7900) & ChrW(7902) & ChrW(7904) & _
                    ChrW(7906) & ChrW(7912) & ChrW(7914) & ChrW(7916) & ChrW(7918) & ChrW(7920) & ChrW(193) & _
                    ChrW(192) & ChrW(7842) & ChrW(195) & ChrW(7840) & ChrW(258) & ChrW(194) & ChrW(272) & _
                    ChrW(201) & ChrW(200) & ChrW(7866) & ChrW(7868) & ChrW(7864) & ChrW(202) & ChrW(205) & _
                    ChrW(204) & ChrW(7880) & ChrW(296) & ChrW(7882) & ChrW(211) & ChrW(210) & ChrW(7886) & _
                    ChrW(213) & ChrW(7884) & ChrW(212) & ChrW(416) & ChrW(218) & ChrW(217) & ChrW(7910) & _
                    ChrW(360) & ChrW(7908) & ChrW(431) & ChrW(221) & ChrW(7922) & ChrW(7926) & ChrW(7928) & ChrW(7924) & Chr(13)

    s = Left(str, 1)
    If InStr(strViet, s) > 0 Then
        isVNChar = True
    Else
        isVNChar = False
    End If
    
    Exit Function

ErrorHandler:
    DebugLog "isVNChar: Lỗi - " & Err.Description
    isVNChar = False
End Function

' ===================================================================
' Hàm: DebugLog
' Mô tả: Ghi log debug ra file và Immediate Window
' Tham số: message - Thông điệp cần ghi log
' ===================================================================
Sub DebugLog(ByVal message As String)
    If Not DEBUG_MODE Then Exit Sub
    
    On Error Resume Next
    
    Dim timestamp As String
    timestamp = Format(Now, "yyyy-mm-dd hh:nn:ss")
    
    ' Ghi ra Immediate Window
    Debug.Print timestamp & " - " & message
    
    ' Ghi ra file log (tùy chọn)
    Dim fileNum As Integer
    fileNum = FreeFile
    
    ' Tạo thư mục nếu chưa có
    If Dir("C:\temp", vbDirectory) = "" Then
        MkDir "C:\temp"
    End If
    
    Open LOG_FILE_PATH For Append As #fileNum
    Print #fileNum, timestamp & " - " & message
    Close #fileNum
End Sub

' ===================================================================
' Hàm: TestVietNameseSupport
' Mô tả: Hàm test các chức năng hỗ trợ tiếng Việt
' ===================================================================
Sub TestVietNameseSupport()
    DebugLog "=== BẮT ĐẦU TEST VIETNAMESE SUPPORT ==="
    
    ' Test 1: Kiểm tra ký tự tiếng Việt
    DebugLog "Test 1: Kiểm tra ký tự tiếng Việt"
    DebugLog "Ký tự 'ă': " & isVNChar("ă")
    DebugLog "Ký tự 'â': " & isVNChar("â")
    DebugLog "Ký tự 'ê': " & isVNChar("ê")
    DebugLog "Ký tự 'ô': " & isVNChar("ô")
    DebugLog "Ký tự 'ư': " & isVNChar("ư")
    DebugLog "Ký tự 'a': " & isVNChar("a")
    
    ' Test 2: Chuyển đổi chuỗi tiếng Việt
    DebugLog "Test 2: Chuyển đổi chuỗi tiếng Việt"
    Dim testString As String
    testString = "Nguyễn Văn An"
    DebugLog "Chuỗi gốc: " & testString
    DebugLog "Chuỗi chuyển đổi: " & convertVNtoVBA(testString)
    
    ' Test 3: Chuỗi rỗng
    DebugLog "Test 3: Chuỗi rỗng"
    DebugLog "Kết quả: " & convertVNtoVBA("")
    
    DebugLog "=== KẾT THÚC TEST VIETNAMESE SUPPORT ==="
    
    MsgBox "Test hoàn thành! Kiểm tra Immediate Window (Ctrl+G) để xem kết quả chi tiết.", vbInformation, "Test Vietnamese Support"
End Sub
