Attribute VB_Name = "VietNameseSupport"
' ===================================================================
' Module: VietNameseSupport
' Mô tả: Hỗ trợ xử lý tiếng Việt trong VBA
' Tác giả: Auto Generated
' Ngày tạo: 2025-01-23
' ===================================================================

Option Explicit

' Biến toàn cục để debug
Public Const DEBUG_MODE As Boolean = True
Public Const LOG_FILE_PATH As String = "C:\temp\excel_addon_debug.log"

' ===================================================================
' Hàm: convertVNtoVBA
' Mô tả: Chuyển đổi chuỗi tiếng Việt sang định dạng VBA
' Tham số: str - Chuỗi cần chuyển đổi
' Trả về: Chuỗi đã được chuyển đổi
' ===================================================================
Function convertVNtoVBA(ByVal str As String) As String
    On Error GoTo ErrorHandler
    
    DebugLog "convertVNtoVBA: Bắt đầu chuyển đổi chuỗi: " & str
    
    Dim isVN1 As Boolean, isVN2 As Boolean, isVar As Boolean
    Dim indexChar As Long, indexStart As Long
    Dim s1 As String
    
    If str = vbNullString Then
        convertVNtoVBA = """"""
        DebugLog "convertVNtoVBA: Chuỗi rỗng, trả về chuỗi trống"
        Exit Function
    End If
        
    isVar = False
       
    str = Replace(str, "//", Chr(13)) & " "
    
    If isVNChar(Left(str, 1)) = False Then convertVNtoVBA = """"
    indexStart = 1
    If Left(str, 1) = "<" Then
        isVar = True
        indexStart = 2
    End If
         
    For indexChar = indexStart To Len(str) - 1
        s1 = Mid(str, indexChar, 1)
           
        If InStr("<>", s1) > 0 Then
            Select Case s1
            Case "<"
                If Not isVNChar(Mid(str, indexChar - 1, 1)) Then
                    convertVNtoVBA = convertVNtoVBA & """ & " & ""
                Else
                    convertVNtoVBA = convertVNtoVBA & " & "
                End If
                        
                isVar = True
                
            Case ">"
                If Not isVNChar(Mid(str, indexChar + 1, 1)) Then
                    convertVNtoVBA = convertVNtoVBA & " & """
                Else
                    convertVNtoVBA = convertVNtoVBA & " & "
                End If
                
                isVar = False
            End Select
        Else
                  
            Select Case isVar
            Case False
                isVN1 = isVNChar(Mid(str, indexChar, 1))
                isVN2 = isVNChar(Mid(str, indexChar + 1, 1))

                If isVN1 = True And isVN2 = True Then
                    convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") & "
                ElseIf isVN1 = True And isVN2 = False Then
                    If Mid(str, indexChar + 1, 1) <> "<" Then
                        convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") & """
                    Else
                        convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") "
                    End If
                ElseIf isVN1 = False And isVN2 = True Then
                    convertVNtoVBA = convertVNtoVBA & s1 & """ & "
                Else
                    convertVNtoVBA = convertVNtoVBA & s1
                End If
                
            Case True
            
                convertVNtoVBA = convertVNtoVBA & s1
            
            End Select
        End If
    Next indexChar

    If Right(convertVNtoVBA, 4) = " & """ Then
        convertVNtoVBA = Left(convertVNtoVBA, Len(convertVNtoVBA) - 4)
    Else
        convertVNtoVBA = convertVNtoVBA & """"
    End If
    
    DebugLog "convertVNtoVBA: Kết quả chuyển đổi: " & convertVNtoVBA
    Exit Function

ErrorHandler:
    DebugLog "convertVNtoVBA: Lỗi - " & Err.Description
    convertVNtoVBA = str ' Trả về chuỗi gốc nếu có lỗi
End Function

' ===================================================================
' Hàm: isVNChar
' Mô tả: Kiểm tra ký tự có phải là ký tự tiếng Việt không
' Tham số: str - Ký tự cần kiểm tra
' Trả về: True nếu là ký tự tiếng Việt, False nếu không
' ===================================================================
Function isVNChar(ByVal str As String) As Boolean
    On Error GoTo ErrorHandler

    If Len(str) = 0 Then
        isVNChar = False
        Exit Function
    End If

    Dim charCode As Long
    charCode = AscW(Left(str, 1))

    ' Kiểm tra các mã Unicode của ký tự tiếng Việt
    ' Các ký tự có dấu: à, á, ả, ã, ạ, ă, ằ, ắ, ẳ, ẵ, ặ, â, ầ, ấ, ẩ, ẫ, ậ
    ' è, é, ẻ, ẽ, ẹ, ê, ề, ế, ể, ễ, ệ
    ' ì, í, ỉ, ĩ, ị
    ' ò, ó, ỏ, õ, ọ, ô, ồ, ố, ổ, ỗ, ộ, ơ, ờ, ớ, ở, ỡ, ợ
    ' ù, ú, ủ, ũ, ụ, ư, ừ, ứ, ử, ữ, ự
    ' ỳ, ý, ỷ, ỹ, ỵ
    ' đ, Đ

    Select Case charCode
        ' Chữ thường có dấu
        Case 224, 225, 7843, 227, 7841  ' à, á, ả, ã, ạ
            isVNChar = True
        Case 259, 7857, 7855, 7859, 7861, 7863  ' ă, ằ, ắ, ẳ, ẵ, ặ
            isVNChar = True
        Case 226, 7847, 7845, 7849, 7851, 7853  ' â, ầ, ấ, ẩ, ẫ, ậ
            isVNChar = True
        Case 232, 233, 7867, 7869, 7865  ' è, é, ẻ, ẽ, ẹ
            isVNChar = True
        Case 234, 7873, 7871, 7875, 7877, 7879  ' ê, ề, ế, ể, ễ, ệ
            isVNChar = True
        Case 236, 237, 7881, 297, 7883  ' ì, í, ỉ, ĩ, ị
            isVNChar = True
        Case 242, 243, 7887, 245, 7885  ' ò, ó, ỏ, õ, ọ
            isVNChar = True
        Case 244, 7891, 7889, 7893, 7895, 7897  ' ô, ồ, ố, ổ, ỗ, ộ
            isVNChar = True
        Case 417, 7901, 7899, 7903, 7905, 7907  ' ơ, ờ, ớ, ở, ỡ, ợ
            isVNChar = True
        Case 249, 250, 7911, 361, 7909  ' ù, ú, ủ, ũ, ụ
            isVNChar = True
        Case 432, 7915, 7913, 7917, 7919, 7921  ' ư, ừ, ứ, ử, ữ, ự
            isVNChar = True
        Case 7923, 253, 7927, 7929, 7925  ' ỳ, ý, ỷ, ỹ, ỵ
            isVNChar = True
        Case 273  ' đ
            isVNChar = True

        ' Chữ hoa có dấu
        Case 192, 193, 7842, 195, 7840  ' À, Á, Ả, Ã, Ạ
            isVNChar = True
        Case 258, 7856, 7854, 7858, 7860, 7862  ' Ă, Ằ, Ắ, Ẳ, Ẵ, Ặ
            isVNChar = True
        Case 194, 7846, 7844, 7848, 7850, 7852  ' Â, Ầ, Ấ, Ẩ, Ẫ, Ậ
            isVNChar = True
        Case 200, 201, 7866, 7868, 7864  ' È, É, Ẻ, Ẽ, Ẹ
            isVNChar = True
        Case 202, 7872, 7870, 7874, 7876, 7878  ' Ê, Ề, Ế, Ể, Ễ, Ệ
            isVNChar = True
        Case 204, 205, 7880, 296, 7882  ' Ì, Í, Ỉ, Ĩ, Ị
            isVNChar = True
        Case 210, 211, 7886, 213, 7884  ' Ò, Ó, Ỏ, Õ, Ọ
            isVNChar = True
        Case 212, 7890, 7888, 7892, 7894, 7896  ' Ô, Ồ, Ố, Ổ, Ỗ, Ộ
            isVNChar = True
        Case 416, 7900, 7898, 7902, 7904, 7906  ' Ơ, Ờ, Ớ, Ở, Ỡ, Ợ
            isVNChar = True
        Case 217, 218, 7910, 360, 7908  ' Ù, Ú, Ủ, Ũ, Ụ
            isVNChar = True
        Case 431, 7914, 7912, 7916, 7918, 7920  ' Ư, Ừ, Ứ, Ử, Ữ, Ự
            isVNChar = True
        Case 7922, 221, 7926, 7928, 7924  ' Ỳ, Ý, Ỷ, Ỹ, Ỵ
            isVNChar = True
        Case 272  ' Đ
            isVNChar = True
        Case Else
            isVNChar = False
    End Select

    Exit Function

ErrorHandler:
    isVNChar = False
End Function

' ===================================================================
' Hàm: DebugLog
' Mô tả: Ghi log debug ra Immediate Window (không ghi file để tránh lỗi encoding)
' Tham số: message - Thông điệp cần ghi log
' ===================================================================
Sub DebugLog(ByVal message As String)
    If Not DEBUG_MODE Then Exit Sub

    On Error Resume Next

    Dim timestamp As String
    timestamp = Format(Now, "yyyy-mm-dd hh:nn:ss")

    ' Chỉ ghi ra Immediate Window để tránh lỗi encoding
    Debug.Print timestamp & " - " & message
End Sub

' ===================================================================
' Hàm: DebugLogSafe
' Mô tả: Ghi log an toàn với encoding ASCII
' Tham số: message - Thông điệp cần ghi log
' ===================================================================
Sub DebugLogSafe(ByVal message As String)
    If Not DEBUG_MODE Then Exit Sub

    On Error Resume Next

    Dim timestamp As String
    timestamp = Format(Now, "yyyy-mm-dd hh:nn:ss")

    ' Chuyển đổi message sang ASCII an toàn
    Dim safeMessage As String
    safeMessage = ConvertToSafeASCII(message)

    ' Ghi ra Immediate Window
    Debug.Print timestamp & " - " & safeMessage
End Sub

' ===================================================================
' Hàm: ConvertToSafeASCII
' Mô tả: Chuyển đổi chuỗi tiếng Việt sang ASCII an toàn
' Tham số: text - Chuỗi cần chuyển đổi
' Trả về: Chuỗi ASCII an toàn
' ===================================================================
Function ConvertToSafeASCII(ByVal text As String) As String
    Dim result As String
    Dim i As Long
    Dim char As String
    Dim charCode As Long

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        charCode = AscW(char)

        ' Nếu là ký tự ASCII cơ bản (32-126) thì giữ nguyên
        If charCode >= 32 And charCode <= 126 Then
            result = result & char
        Else
            ' Chuyển ký tự Unicode thành mã [U+xxxx]
            result = result & "[U+" & Right("0000" & Hex(charCode), 4) & "]"
        End If
    Next i

    ConvertToSafeASCII = result
End Function

' ===================================================================
' Hàm: TestVietNameseSupport
' Mô tả: Hàm test các chức năng hỗ trợ tiếng Việt
' ===================================================================
Sub TestVietNameseSupport()
    DebugLogSafe "=== BAT DAU TEST VIETNAMESE SUPPORT ==="

    ' Test 1: Kiểm tra ký tự tiếng Việt
    DebugLogSafe "Test 1: Kiem tra ky tu tieng Viet"
    DebugLogSafe "Ky tu 'a' (thuong): " & isVNChar("ă")
    DebugLogSafe "Ky tu 'a' (mu): " & isVNChar("â")
    DebugLogSafe "Ky tu 'e' (mu): " & isVNChar("ê")
    DebugLogSafe "Ky tu 'o' (mu): " & isVNChar("ô")
    DebugLogSafe "Ky tu 'u' (horn): " & isVNChar("ư")
    DebugLogSafe "Ky tu 'd' (bar): " & isVNChar("đ")
    DebugLogSafe "Ky tu 'a' (binh thuong): " & isVNChar("a")

    ' Test 2: Chuyển đổi chuỗi tiếng Việt
    DebugLogSafe "Test 2: Chuyen doi chuoi tieng Viet"
    Dim testString As String
    testString = "Nguyen Van An"
    DebugLogSafe "Chuoi goc (ASCII): " & testString
    DebugLogSafe "Chuoi chuyen doi: " & convertVNtoVBA(testString)

    ' Test 3: Chuỗi có dấu
    testString = "Nguyễn Văn An"
    DebugLogSafe "Chuoi co dau (Unicode): " & ConvertToSafeASCII(testString)
    DebugLogSafe "Chuoi chuyen doi VBA: " & convertVNtoVBA(testString)

    ' Test 4: Chuỗi rỗng
    DebugLogSafe "Test 4: Chuoi rong"
    DebugLogSafe "Ket qua: " & convertVNtoVBA("")

    ' Test 5: Kiểm tra từng ký tự cụ thể
    DebugLogSafe "Test 5: Kiem tra chi tiet"
    Dim testChars As String
    testChars = "aăâeêiouôơưđ"
    Dim i As Long
    For i = 1 To Len(testChars)
        Dim char As String
        char = Mid(testChars, i, 1)
        DebugLogSafe "Char '" & ConvertToSafeASCII(char) & "' -> isVN: " & isVNChar(char)
    Next i

    DebugLogSafe "=== KET THUC TEST VIETNAMESE SUPPORT ==="

    MsgBox "Test hoan thanh! Kiem tra Immediate Window (Ctrl+G) de xem ket qua chi tiet.", vbInformation, "Test Vietnamese Support"
End Sub
