Attribute VB_Name = "TestRunner"
' ===================================================================
' Module: TestRunner
' Description: Comprehensive test runner module
' Author: Auto Generated
' Date: 2025-01-23
' ===================================================================

Option Explicit

' ===================================================================
' Function: RunAllTests
' Description: Run all tests
' ===================================================================
Sub RunAllTests()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "START RUNNING ALL TESTS"
    Debug.Print "========================================="
    
    ' Test 1: Vietnamese Support
    Debug.Print ""
    Debug.Print "--- TEST 1: VIETNAMESE SUPPORT ---"
    TestVietNameseSupport
    
    ' Test 2: Student Manager
    Debug.Print ""
    Debug.Print "--- TEST 2: STUDENT MANAGER ---"
    TestStudentManager
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "ALL TESTS COMPLETED"
    Debug.Print "========================================="
    
    MsgBox "All tests completed! Check Immediate Window for results.", vbInformation, "Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "ERROR IN TEST PROCESS: " & Err.Description
    MsgBox "Error in test process: " & Err.Description, vbCritical, "Test Error"
End Sub

' ===================================================================
' Function: QuickTest
' Description: Quick test of basic functions
' ===================================================================
Sub QuickTest()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "START QUICK TEST"
    Debug.Print "========================================="
    
    ' Test basic Vietnamese characters (using Unicode codes)
    Debug.Print "Test Vietnamese characters:"
    Debug.Print "- Char 'a' (breve - U+0103): " & isVNChar(ChrW(259))  ' a
    Debug.Print "- Char 'a' (normal): " & isVNChar("a")
    Debug.Print "- Char 'd' (stroke - U+0111): " & isVNChar(ChrW(273))     ' d
    
    ' Test simple string conversion
    Debug.Print ""
    Debug.Print "Test string conversion:"
    Dim testStr As String
    testStr = "Hello World"
    Debug.Print "- ASCII string: " & testStr & " -> " & convertVNtoVBA(testStr)
    
    ' Test sheet checking
    Debug.Print ""
    Debug.Print "Test sheet checking:"
    Debug.Print "- Sheet 'DanhSachHocVien' exists: " & SheetExists("DanhSachHocVien")
    Debug.Print "- Sheet 'LichSuThayDoi' exists: " & SheetExists("LichSuThayDoi")
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "QUICK TEST COMPLETED"
    Debug.Print "========================================="
    
    MsgBox "Quick test completed! Check Immediate Window for results.", vbInformation, "Quick Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "ERROR IN QUICK TEST: " & Err.Description
    MsgBox "Error in quick test: " & Err.Description, vbCritical, "Quick Test Error"
End Sub

' ===================================================================
' Function: TestUnicodeChars
' Description: Detailed test of Unicode Vietnamese characters
' ===================================================================
Sub TestUnicodeChars()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "START TEST UNICODE CHARACTERS"
    Debug.Print "========================================="
    
    ' Test character list (using Unicode codes)
    Dim testChars As Variant
    Dim testNames As Variant
    
    ' Unicode character array
    testChars = Array("a", ChrW(259), ChrW(226), "e", ChrW(234), "i", "o", ChrW(244), ChrW(417), "u", ChrW(432), "y", ChrW(273), _
                     "A", ChrW(258), ChrW(194), "E", ChrW(202), "I", "O", ChrW(212), ChrW(416), "U", ChrW(431), "Y", ChrW(272))
    
    ' Corresponding name array
    testNames = Array("a", "a(259)", "a(226)", "e", "e(234)", "i", "o", "o(244)", "o(417)", "u", "u(432)", "y", "d(273)", _
                     "A", "A(258)", "A(194)", "E", "E(202)", "I", "O", "O(212)", "O(416)", "U", "U(431)", "Y", "D(272)")
    
    Dim i As Long
    For i = 0 To UBound(testChars)
        Dim char As String
        char = CStr(testChars(i))
        Dim charName As String
        charName = CStr(testNames(i))
        Dim charCode As Long
        charCode = AscW(char)
        Dim isVN As Boolean
        isVN = isVNChar(char)
        
        Debug.Print "Char: " & charName & " | Code: " & charCode & " | IsVN: " & isVN
    Next i
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "UNICODE CHARACTERS TEST COMPLETED"
    Debug.Print "========================================="
    
    MsgBox "Unicode test completed! Check Immediate Window for results.", vbInformation, "Unicode Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "ERROR IN UNICODE TEST: " & Err.Description
    MsgBox "Error in unicode test: " & Err.Description, vbCritical, "Unicode Test Error"
End Sub

' ===================================================================
' Function: ClearDebugWindow
' Description: Clear Immediate Window content (manual instruction)
' ===================================================================
Sub ClearDebugWindow()
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "TO CLEAR IMMEDIATE WINDOW:"
    Debug.Print "1. Press Ctrl+G to open Immediate Window"
    Debug.Print "2. Select all content (Ctrl+A)"
    Debug.Print "3. Press Delete"
    Debug.Print "========================================="
    Debug.Print ""
End Sub

' ===================================================================
' Function: TestBasicUnicode
' Description: Basic test of Unicode Vietnamese characters
' ===================================================================
Sub TestBasicUnicode()
    On Error GoTo ErrorHandler
    
    Debug.Print "========================================="
    Debug.Print "START BASIC UNICODE TEST"
    Debug.Print "========================================="
    
    ' Test basic characters
    Debug.Print "Test basic characters:"
    Debug.Print "- a (97): " & isVNChar("a") & " | Expected: False"
    Debug.Print "- a (259): " & isVNChar(ChrW(259)) & " | Expected: True"
    Debug.Print "- a (226): " & isVNChar(ChrW(226)) & " | Expected: True"
    Debug.Print "- d (273): " & isVNChar(ChrW(273)) & " | Expected: True"
    Debug.Print "- e (234): " & isVNChar(ChrW(234)) & " | Expected: True"
    Debug.Print "- o (244): " & isVNChar(ChrW(244)) & " | Expected: True"
    Debug.Print "- o (417): " & isVNChar(ChrW(417)) & " | Expected: True"
    Debug.Print "- u (432): " & isVNChar(ChrW(432)) & " | Expected: True"
    
    ' Test uppercase
    Debug.Print ""
    Debug.Print "Test uppercase:"
    Debug.Print "- A (65): " & isVNChar("A") & " | Expected: False"
    Debug.Print "- A (258): " & isVNChar(ChrW(258)) & " | Expected: True"
    Debug.Print "- A (194): " & isVNChar(ChrW(194)) & " | Expected: True"
    Debug.Print "- D (272): " & isVNChar(ChrW(272)) & " | Expected: True"
    
    ' Test creating Vietnamese string
    Debug.Print ""
    Debug.Print "Test creating Vietnamese string:"
    Dim vnString As String
    vnString = "Vi" & ChrW(7879) & "t Nam"  ' Viet Nam
    Debug.Print "String: " & ConvertToSafeASCII(vnString)
    Debug.Print "Convert VBA: " & convertVNtoVBA(vnString)
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "BASIC UNICODE TEST COMPLETED"
    Debug.Print "========================================="
    
    MsgBox "Basic Unicode test completed! Check Immediate Window for results.", vbInformation, "Basic Unicode Test Complete"
    
    Exit Sub

ErrorHandler:
    Debug.Print "ERROR IN BASIC UNICODE TEST: " & Err.Description
    MsgBox "Error in basic unicode test: " & Err.Description, vbCritical, "Basic Unicode Test Error"
End Sub
