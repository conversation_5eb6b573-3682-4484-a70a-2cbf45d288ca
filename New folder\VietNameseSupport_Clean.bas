Attribute VB_Name = "VietNameseSupport"
' ===================================================================
' Module: VietNameseSupport
' Description: Vietnamese character support for VBA
' Author: Auto Generated
' Date: 2025-01-23
' ===================================================================

Option Explicit

' Global variables for debug
Public Const DEBUG_MODE As Boolean = True

' ===================================================================
' Function: convertVNtoVBA
' Description: Convert Vietnamese string to VBA format
' Parameter: str - String to convert
' Return: Converted string
' ===================================================================
Function convertVNtoVBA(ByVal str As String) As String
    On Error GoTo ErrorHandler
    
    DebugLogSafe "convertVNtoVBA: Start converting string: " & str
    
    Dim isVN1 As Boolean, isVN2 As Boolean, isVar As Boolean
    Dim indexChar As Long, indexStart As Long
    Dim s1 As String
    
    If str = vbNullString Then
        convertVNtoVBA = """"""
        DebugLogSafe "convertVNtoVBA: Empty string, return empty"
        Exit Function
    End If
        
    isVar = False
       
    str = Replace(str, "//", Chr(13)) & " "
    
    If isVNChar(Left(str, 1)) = False Then convertVNtoVBA = """"
    indexStart = 1
    If Left(str, 1) = "<" Then
        isVar = True
        indexStart = 2
    End If
         
    For indexChar = indexStart To Len(str) - 1
        s1 = Mid(str, indexChar, 1)
           
        If InStr("<>", s1) > 0 Then
            Select Case s1
            Case "<"
                If Not isVNChar(Mid(str, indexChar - 1, 1)) Then
                    convertVNtoVBA = convertVNtoVBA & """ & " & ""
                Else
                    convertVNtoVBA = convertVNtoVBA & " & "
                End If
                        
                isVar = True
                
            Case ">"
                If Not isVNChar(Mid(str, indexChar + 1, 1)) Then
                    convertVNtoVBA = convertVNtoVBA & " & """
                Else
                    convertVNtoVBA = convertVNtoVBA & " & "
                End If
                
                isVar = False
            End Select
        Else
                  
            Select Case isVar
            Case False
                isVN1 = isVNChar(Mid(str, indexChar, 1))
                isVN2 = isVNChar(Mid(str, indexChar + 1, 1))

                If isVN1 = True And isVN2 = True Then
                    convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") & "
                ElseIf isVN1 = True And isVN2 = False Then
                    If Mid(str, indexChar + 1, 1) <> "<" Then
                        convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") & """
                    Else
                        convertVNtoVBA = convertVNtoVBA & "ChrW(" & AscW(s1) & ") "
                    End If
                ElseIf isVN1 = False And isVN2 = True Then
                    convertVNtoVBA = convertVNtoVBA & s1 & """ & "
                Else
                    convertVNtoVBA = convertVNtoVBA & s1
                End If
                
            Case True
            
                convertVNtoVBA = convertVNtoVBA & s1
            
            End Select
        End If
    Next indexChar

    If Right(convertVNtoVBA, 4) = " & """ Then
        convertVNtoVBA = Left(convertVNtoVBA, Len(convertVNtoVBA) - 4)
    Else
        convertVNtoVBA = convertVNtoVBA & """"
    End If
    
    DebugLogSafe "convertVNtoVBA: Result: " & convertVNtoVBA
    Exit Function

ErrorHandler:
    DebugLogSafe "convertVNtoVBA: Error - " & Err.Description
    convertVNtoVBA = str ' Return original string if error
End Function

' ===================================================================
' Function: isVNChar
' Description: Check if character is Vietnamese character
' Parameter: str - Character to check
' Return: True if Vietnamese character, False if not
' ===================================================================
Function isVNChar(ByVal str As String) As Boolean
    On Error GoTo ErrorHandler
    
    If Len(str) = 0 Then
        isVNChar = False
        Exit Function
    End If
    
    Dim charCode As Long
    charCode = AscW(Left(str, 1))
    
    ' Check Unicode codes for Vietnamese characters
    ' Characters with diacritics: a, a, a, e, e, i, o, o, o, u, u, y, d, D
    
    Select Case charCode
        ' Lowercase with diacritics
        Case 224, 225, 7843, 227, 7841  ' a, a, a, a, a
            isVNChar = True
        Case 259, 7857, 7855, 7859, 7861, 7863  ' a, a, a, a, a, a
            isVNChar = True
        Case 226, 7847, 7845, 7849, 7851, 7853  ' a, a, a, a, a, a
            isVNChar = True
        Case 232, 233, 7867, 7869, 7865  ' e, e, e, e, e
            isVNChar = True
        Case 234, 7873, 7871, 7875, 7877, 7879  ' e, e, e, e, e, e
            isVNChar = True
        Case 236, 237, 7881, 297, 7883  ' i, i, i, i, i
            isVNChar = True
        Case 242, 243, 7887, 245, 7885  ' o, o, o, o, o
            isVNChar = True
        Case 244, 7891, 7889, 7893, 7895, 7897  ' o, o, o, o, o, o
            isVNChar = True
        Case 417, 7901, 7899, 7903, 7905, 7907  ' o, o, o, o, o, o
            isVNChar = True
        Case 249, 250, 7911, 361, 7909  ' u, u, u, u, u
            isVNChar = True
        Case 432, 7915, 7913, 7917, 7919, 7921  ' u, u, u, u, u, u
            isVNChar = True
        Case 7923, 253, 7927, 7929, 7925  ' y, y, y, y, y
            isVNChar = True
        Case 273  ' d
            isVNChar = True
            
        ' Uppercase with diacritics
        Case 192, 193, 7842, 195, 7840  ' A, A, A, A, A
            isVNChar = True
        Case 258, 7856, 7854, 7858, 7860, 7862  ' A, A, A, A, A, A
            isVNChar = True
        Case 194, 7846, 7844, 7848, 7850, 7852  ' A, A, A, A, A, A
            isVNChar = True
        Case 200, 201, 7866, 7868, 7864  ' E, E, E, E, E
            isVNChar = True
        Case 202, 7872, 7870, 7874, 7876, 7878  ' E, E, E, E, E, E
            isVNChar = True
        Case 204, 205, 7880, 296, 7882  ' I, I, I, I, I
            isVNChar = True
        Case 210, 211, 7886, 213, 7884  ' O, O, O, O, O
            isVNChar = True
        Case 212, 7890, 7888, 7892, 7894, 7896  ' O, O, O, O, O, O
            isVNChar = True
        Case 416, 7900, 7898, 7902, 7904, 7906  ' O, O, O, O, O, O
            isVNChar = True
        Case 217, 218, 7910, 360, 7908  ' U, U, U, U, U
            isVNChar = True
        Case 431, 7914, 7912, 7916, 7918, 7920  ' U, U, U, U, U, U
            isVNChar = True
        Case 7922, 221, 7926, 7928, 7924  ' Y, Y, Y, Y, Y
            isVNChar = True
        Case 272  ' D
            isVNChar = True
        Case Else
            isVNChar = False
    End Select
    
    Exit Function

ErrorHandler:
    isVNChar = False
End Function

' ===================================================================
' Function: DebugLogSafe
' Description: Safe logging to Immediate Window only
' Parameter: message - Message to log
' ===================================================================
Sub DebugLogSafe(ByVal message As String)
    If Not DEBUG_MODE Then Exit Sub
    
    On Error Resume Next
    
    Dim timestamp As String
    timestamp = Format(Now, "yyyy-mm-dd hh:nn:ss")
    
    ' Only log to Immediate Window to avoid encoding issues
    Debug.Print timestamp & " - " & message
End Sub

' ===================================================================
' Function: ConvertToSafeASCII
' Description: Convert Vietnamese string to safe ASCII representation
' Parameter: text - String to convert
' Return: Safe ASCII string
' ===================================================================
Function ConvertToSafeASCII(ByVal text As String) As String
    Dim result As String
    Dim i As Long
    Dim char As String
    Dim charCode As Long
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        charCode = AscW(char)
        
        ' If basic ASCII character (32-126) keep as is
        If charCode >= 32 And charCode <= 126 Then
            result = result & char
        Else
            ' Convert Unicode character to [U+xxxx] format
            result = result & "[U+" & Right("0000" & Hex(charCode), 4) & "]"
        End If
    Next i
    
    ConvertToSafeASCII = result
End Function

' ===================================================================
' Function: TestVietNameseSupport
' Description: Test Vietnamese support functions
' ===================================================================
Sub TestVietNameseSupport()
    DebugLogSafe "=== START TEST VIETNAMESE SUPPORT ==="
    
    ' Test 1: Check Vietnamese characters (using Unicode codes)
    DebugLogSafe "Test 1: Check Vietnamese characters"
    DebugLogSafe "Char 'a' (breve U+0103): " & isVNChar(ChrW(259))   ' a
    DebugLogSafe "Char 'a' (circumflex U+00E2): " & isVNChar(ChrW(226))       ' a
    DebugLogSafe "Char 'e' (circumflex U+00EA): " & isVNChar(ChrW(234))       ' e
    DebugLogSafe "Char 'o' (circumflex U+00F4): " & isVNChar(ChrW(244))       ' o
    DebugLogSafe "Char 'u' (horn U+01B0): " & isVNChar(ChrW(432))     ' u
    DebugLogSafe "Char 'd' (stroke U+0111): " & isVNChar(ChrW(273))      ' d
    DebugLogSafe "Char 'a' (normal): " & isVNChar("a")
    
    ' Test 2: Convert simple string
    DebugLogSafe "Test 2: Convert Vietnamese string"
    Dim testString As String
    testString = "Nguyen Van An"
    DebugLogSafe "Original string (ASCII): " & testString
    DebugLogSafe "Converted string: " & convertVNtoVBA(testString)
    
    ' Test 3: String with diacritics (created using Unicode)
    testString = "Nguy" & ChrW(7877) & "n V" & ChrW(259) & "n An"  ' Nguyen Van An
    DebugLogSafe "String with diacritics (Unicode): " & ConvertToSafeASCII(testString)
    DebugLogSafe "Converted VBA string: " & convertVNtoVBA(testString)
    
    ' Test 4: Empty string
    DebugLogSafe "Test 4: Empty string"
    DebugLogSafe "Result: " & convertVNtoVBA("")
    
    ' Test 5: Check specific characters (using Unicode codes)
    DebugLogSafe "Test 5: Check details"
    Dim testCharArray As Variant
    Dim testNameArray As Variant
    
    ' Unicode character array
    testCharArray = Array("a", ChrW(259), ChrW(226), "e", ChrW(234), "i", "o", "u", ChrW(244), ChrW(417), ChrW(432), ChrW(273))
    testNameArray = Array("a", "a(259)", "a(226)", "e", "e(234)", "i", "o", "u", "o(244)", "o(417)", "u(432)", "d(273)")
    
    Dim i As Long
    For i = 0 To UBound(testCharArray)
        Dim char As String
        char = CStr(testCharArray(i))
        Dim charName As String
        charName = CStr(testNameArray(i))
        DebugLogSafe "Char '" & charName & "' -> isVN: " & isVNChar(char)
    Next i
    
    DebugLogSafe "=== END TEST VIETNAMESE SUPPORT ==="
    
    MsgBox "Test completed! Check Immediate Window (Ctrl+G) for detailed results.", vbInformation, "Test Vietnamese Support"
End Sub
