# HƯỚNG DẪN CÀI ĐẶT VÀ SỬ DỤNG ADDON EXCEL QUẢN LÝ HỌC VIÊN

## BƯỚC 1: CÀI ĐẶT

### 1.1. <PERSON><PERSON><PERSON> bị
- Microsoft Excel 2016 trở lên
- Bật Developer Mode trong Excel
- Cho phép chạy Macro

### 1.2. Import các file VBA
1. Mở Excel
2. Nhấn `Alt + F11` để mở VBA Editor
3. Trong VBA Editor:
   - <PERSON>lick chuột phải vào VBAProject
   - Chọn Import File
   - Import lần lượt các file:
     - `VietNameseSupport.bas`
     - `StudentManager.bas`
     - `TestRunner.bas`

### 1.3. Thi<PERSON>t lập bảo mật Macro
1. Trong Excel, vào File > Options > Trust Center > Trust Center Settings
2. Chọn Macro Settings
3. Chọn "Enable all macros" (hoặc "Disable all macros with notification")

## BƯỚC 2: KIỂM TRA CÀI ĐẶT

### 2.1. Test nhanh (Khuyến nghị)
1. Trong VBA Editor, nhấn `Ctrl + G` để mở Immediate Window
2. <PERSON><PERSON><PERSON> <PERSON><PERSON>: `TestBasicUnicode` (test cơ bản nhất)
3. Hoặc chạy: `QuickTest` (test tổng quát)
4. Kiểm tra kết quả trong Immediate Window

### 2.2. Test đầy đủ
1. Chạy lệnh: `RunAllTests` (test tất cả)
2. Hoặc test từng phần:
   - `TestVietNameseSupport` (test tiếng Việt)
   - `TestStudentManager` (test quản lý học viên)
   - `TestUnicodeChars` (test chi tiết Unicode)

### 2.3. Kiểm tra kết quả
- Sheet "DanhSachHocVien" được tạo
- Sheet "LichSuThayDoi" được tạo
- Dữ liệu mẫu được thêm vào
- Log hiển thị bằng tiếng Anh (tránh lỗi encoding)

## BƯỚC 3: SỬ DỤNG CƠ BẢN

### 3.1. Khởi tạo hệ thống
```vba
' Chạy lệnh này để khởi tạo hệ thống lần đầu
InitializeWorkbook
```

### 3.2. Thêm dữ liệu mẫu
```vba
' Chạy lệnh này để thêm dữ liệu mẫu
AddSampleData
```

### 3.3. Cấu trúc dữ liệu
- **STT**: Số thứ tự
- **Họ và tên**: Họ tên đầy đủ của học viên
- **Ngày sinh (Nam)**: Ngày sinh cho học viên nam
- **Ngày sinh (Nữ)**: Ngày sinh cho học viên nữ
- **Chức vụ**: Chức vụ hiện tại
- **Đơn vị**: Đơn vị công tác
- **Lớp học**: Lớp được phân công

## BƯỚC 4: DEBUG VÀ TROUBLESHOOTING

### 4.1. Kiểm tra log
- Log được ghi trong Immediate Window (Ctrl + G)
- **Lưu ý**: Log hiển thị bằng tiếng Anh để tránh lỗi encoding
- Sử dụng `ClearDebugWindow` để xóa log cũ

### 4.2. Các lỗi thường gặp

#### Lỗi: "Compile error"
- **Nguyên nhân**: Thiếu module hoặc import sai
- **Giải pháp**: Kiểm tra lại việc import các file .bas

#### Lỗi: "Runtime error"
- **Nguyên nhân**: Chưa khởi tạo hệ thống
- **Giải pháp**: Chạy `InitializeWorkbook` trước

#### Lỗi: "Encoding tiếng Việt"
- **Nguyên nhân**: VBA sử dụng ANSI encoding, không phải UTF-8
- **Giải pháp**: Đã sửa - sử dụng mã Unicode (ChrW) thay vì ký tự trực tiếp
- **Lưu ý**: Khi import file .bas, ký tự tiếng Việt sẽ bị chuyển đổi sai

### 4.3. Kiểm tra từng bước

#### Bước 1: Kiểm tra module đã import
```vba
' Trong Immediate Window
? "VietNameseSupport module: " & (TypeName(VietNameseSupport) <> "")
? "StudentManager module: " & (TypeName(StudentManager) <> "")
```

#### Bước 2: Kiểm tra hàm tiếng Việt
```vba
' Test ký tự tiếng Việt (sử dụng mã Unicode)
? isVNChar(ChrW(259))  ' ă - Kết quả: True
? isVNChar("a")        ' a - Kết quả: False
? isVNChar(ChrW(273))  ' đ - Kết quả: True
```

#### Bước 3: Kiểm tra chuyển đổi chuỗi
```vba
' Test chuyển đổi (tạo chuỗi bằng Unicode)
Dim vnName As String
vnName = "Nguy" & ChrW(7877) & "n"  ' Nguyễn
? convertVNtoVBA(vnName)
```

## BƯỚC 5: HIỂU VỀ UNICODE VÀ TIẾNG VIỆT

### 5.1. Vấn đề Encoding
- **VBA sử dụng ANSI**: Không hỗ trợ UTF-8 trực tiếp
- **Giải pháp**: Sử dụng `ChrW(mã_unicode)` thay vì ký tự trực tiếp
- **Ví dụ**: `ChrW(259)` thay vì `"ă"`

### 5.2. Bảng mã Unicode tiếng Việt thường dùng
```
ă = ChrW(259)    Ă = ChrW(258)
â = ChrW(226)    Â = ChrW(194)
đ = ChrW(273)    Đ = ChrW(272)
ê = ChrW(234)    Ê = ChrW(202)
ô = ChrW(244)    Ô = ChrW(212)
ơ = ChrW(417)    Ơ = ChrW(416)
ư = ChrW(432)    Ư = ChrW(431)
```

### 5.3. Cách tạo chuỗi tiếng Việt trong VBA
```vba
' Thay vì: "Nguyễn Văn An"
' Sử dụng:
Dim hoTen As String
hoTen = "Nguy" & ChrW(7877) & "n V" & ChrW(259) & "n An"
```

## BƯỚC 6: CẤU HÌNH NÂNG CAO

### 6.1. Thay đổi cấu hình debug
Trong module `VietNameseSupport.bas`:
```vba
' Tắt debug mode
Public Const DEBUG_MODE As Boolean = False

' Thay đổi đường dẫn log
Public Const LOG_FILE_PATH As String = "D:\MyLogs\excel_addon.log"
```

### 6.2. Tùy chỉnh tên sheet
Trong module `StudentManager.bas`:
```vba
' Thay đổi tên sheet
Public Const MAIN_SHEET_NAME As String = "TenSheetMoi"
Public Const HISTORY_SHEET_NAME As String = "LichSuMoi"
```

## BƯỚC 7: KIỂM TRA HOÀN CHỈNH

### Checklist cài đặt thành công:
- [ ] Import được 3 file .bas (VietNameseSupport, StudentManager, TestRunner)
- [ ] Chạy được `TestBasicUnicode` không lỗi
- [ ] Kết quả test hiển thị "Expected: True/False" đúng như mong đợi
- [ ] Chạy được `QuickTest` và `RunAllTests` không lỗi
- [ ] Tạo được sheet "DanhSachHocVien"
- [ ] Tạo được sheet "LichSuThayDoi"
- [ ] Hiển thị được tiếng Việt chính xác trong Excel
- [ ] Log ghi được vào Immediate Window (bằng tiếng Anh)
- [ ] Các hàm isVNChar trả về True cho ký tự tiếng Việt, False cho ký tự thường

### Nếu tất cả đều OK:
✅ **Hệ thống đã sẵn sàng cho bước tiếp theo!**

## LIÊN HỆ HỖ TRỢ
Nếu gặp vấn đề, vui lòng cung cấp:
1. Thông báo lỗi chính xác
2. Nội dung trong Immediate Window
3. Phiên bản Excel đang sử dụng
4. Các bước đã thực hiện

---
**Lưu ý**: Đây là phiên bản đầu tiên (v1.0) - chỉ bao gồm các chức năng cơ bản. Các tính năng nâng cao sẽ được thêm vào các phiên bản tiếp theo.
